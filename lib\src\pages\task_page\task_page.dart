import 'package:flutter/material.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';

class TaskPage extends StatefulWidget {
  const TaskPage({super.key});

  @override
  State<StatefulWidget> createState() => _TaskPageState();
}

class _TaskPageState extends State<TaskPage> {
  //任务状态 1.已领取 2.已完成但没领取 3.未完成

  Map<int, Color> taskStatusButColors = {
    1: const Color(0xFFE4E4E4), // 对应颜色 1
    2: const Color(0xFFFF5B2D), // 对应颜色 2
    3: const Color(0xFFFFB61F), // 对应颜色 3
  };
  Map<int, Color> taskStatusButTextColors = {
    1: const Color(0xFF666666), // 对应颜色 1
    2: const Color(0xFFFFFFFF), // 对应颜色 2
    3: const Color(0xFFFFFFFF), // 对应颜色 3
  };
  Map<int, String> taskStatusButTexts = {
    1: TIM_t('已领取'),
    2: TIM_t('领取'),
    3: TIM_t('去完成'),
  };

  dynamic getTaskMap(Map<int, dynamic> taskStatusMap, int taskStatus) {
    return taskStatusMap[taskStatus];
  }

  Widget _buildShaderText({required Widget child}) {
    return ShaderMask(
        shaderCallback: (bounds) => const LinearGradient(
              colors: [Color(0xFFEE3637), Color(0xFFEEAB36)],
              begin: Alignment.topLeft,
              stops: [0.3, 1],
              end: Alignment.bottomRight,
            ).createShader(bounds),
        child: child);
  }

  Widget _buildTaskStatusBut({required int status}) {
    Color taskButColor = getTaskMap(taskStatusButColors, status);
    Color taskButTextColor = getTaskMap(taskStatusButTextColors, status);
    String taskButText = getTaskMap(taskStatusButTexts, status);
    // 状态按钮
    return Container(
      width: 55,
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: taskButColor,
        borderRadius: BorderRadius.circular(50),
      ),
      child: Text(
        taskButText,
        style: TextStyle(
          fontSize: 10,
          color: taskButTextColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildTaskItem({
    required String taskNumber,
    required String taskDescription,
    //任务状态 1.已领取 2.已完成但没领取 3.未完成
    required int taskStatus,
    bool isNextImg = true,
    String? buttonText,
  }) {
    Map<int, Color> taskStatusLeftColors = {
      1: const Color(0xFFFF5B2D), // 对应颜色 1
      2: Colors.white.withOpacity(0.6), // 对应颜色 2
      3: Colors.white.withOpacity(0.6), // 对应颜色 3
    };
    Map<int, Color> taskStatusLeftTextColors = {
      1: Colors.white, // 对应颜色 1
      2: const Color(0xFFFF5B2D), // 对应颜色 2
      3: const Color(0xFFFF5B2D), // 对应颜色 3
    };

    Map<int, String> taskStatusNextImgs = {
      1: 'assets/task_page/task_next_active.png',
      2: 'assets/task_page/task_next.png',
      3: 'assets/task_page/task_next.png',
    };
    Color taskStatusLeftColor = getTaskMap(taskStatusLeftColors, taskStatus);
    Color taskStatusLeftTextColor =
        getTaskMap(taskStatusLeftTextColors, taskStatus);
    String taskStatusNextImg = getTaskMap(taskStatusNextImgs, taskStatus);

    return Stack(clipBehavior: Clip.none, children: [
      Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.white.withOpacity(0.6),
        ),
        child: Row(
          children: [
            // 任务编号
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: taskStatusLeftColor,
                //边框样式
                border: Border.all(
                  color: const Color(0xFFFF5B2D),
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                taskNumber,
                style: TextStyle(
                  fontSize: 12,
                  color: taskStatusLeftTextColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            const SizedBox(width: 12),

            // 任务描述
            Expanded(
              child: Text(
                taskDescription,
                style: const TextStyle(
                  fontSize: 11,
                  color: Color(0xFF903D1F),
                  height: 1.4,
                ),
              ),
            ),
            const SizedBox(width: 12),
            _buildTaskStatusBut(status: taskStatus),
          ],
        ),
      ),
      if (isNextImg)
        Positioned(left: 30, bottom: -18, child: Image.asset(taskStatusNextImg))
    ]);
  }

  @override
  Widget build(BuildContext context) {
    final List<Map<String, dynamic>> invitedList = [
      {
        'phone': '189*****123',
        'date': '2025/08/20 12:30:12',
        'status': 2,
      },
      {
        'phone': '189*****123',
        'date': '2025/08/20 12:30:12',
        'status': 1,
      },
      {
        'phone': '189*****123',
        'date': '2025/08/20 12:30:12',
        'status': 2,
      },
    ];
    return Stack(
      children: [
        const AppLogo(),
        SingleChildScrollView(
            child: Column(
          children: [
            SizedBox(height: MediaQuery.of(context).padding.top),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 53),
              child: TaskInfoButton(
                amount: '10',
                activatedCount: '50',
              ),
            ),
            SizedBox(height: 8),
            ShaderMask(
                shaderCallback: (bounds) => const LinearGradient(
                      colors: [Color(0xFFFFE786), Color(0xFFFFFFFF)],
                      begin: Alignment.topCenter,
                      stops: [0.3, 1],
                      end: Alignment.bottomCenter,
                    ).createShader(bounds),
                child: Text(TIM_t('天天领红包'),
                    style: const TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ))),
            Text(TIM_t('做任务') + ' ' + TIM_t('领奖励'),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                )),
            SizedBox(height: 166),
            Stack(
              clipBehavior: Clip.none,
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    image: const DecorationImage(
                      image: AssetImage('assets/task_page/task_card_bg.png'),
                      // Use AssetImage instead of Image.asset
                      fit: BoxFit.fill, // Optional, controls how the image fits
                    ),
                  ),
                  child: Column(
                    children: [
                      // 顶部标题栏
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                              padding: const EdgeInsets.symmetric(vertical: 6),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                gradient: const LinearGradient(
                                  colors: [
                                    Color(0xFFFFDFD5),
                                    Color(0x00FDE8F2)
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                              ),
                              child: Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  Row(
                                    children: [
                                      const SizedBox(width: 48),
                                      _buildShaderText(
                                          child: Text(
                                        TIM_t('每日签到'),
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      )),

                                      // const SizedBox(width: 100),
                                      const Spacer(),
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            TIM_t('签到规则'),
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Color(0xFFFD8888),
                                            ),
                                          ),
                                          SizedBox(width: 4),
                                          Icon(
                                            Icons.chevron_right,
                                            color: Color(0xFFFD8888),
                                            size: 16,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  Positioned(
                                      left: 0,
                                      bottom: -8,
                                      child: Container(
                                        child: Image.asset(
                                            width: 42,
                                            height: 42,
                                            'assets/task_page/signIn.png'),
                                      )),
                                ],
                              )),
                          SizedBox(height: 8),
                        ],
                      ),
                      //红包签到栏
                      TaskRedSign()
                    ],
                  ),
                ),
                // Positioned(
                //   left: 0,
                //   right: 0,
                //   bottom: -20,
                //     child: Image.asset("assets/task_page/corner.png"))
              ],
            ),
            SizedBox(height: 8),
            Stack(
              clipBehavior: Clip.none,
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    image: const DecorationImage(
                      image: AssetImage('assets/task_page/task_card_bg.png'),
                      // Use AssetImage instead of Image.asset
                      fit: BoxFit.fill, // Optional, controls how the image fits
                    ),
                  ),
                  child: Column(
                    children: [
                      // 顶部标题栏
                      Column(
                        children: [
                          Container(
                              padding: const EdgeInsets.symmetric(vertical: 6),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                gradient: const LinearGradient(
                                  colors: [
                                    Color(0xFFFFDFD5),
                                    Color(0x00FDE8F2)
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                              ),
                              child: Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  Row(
                                    children: [
                                      const SizedBox(width: 48),
                                      _buildShaderText(
                                          child: Text(
                                        TIM_t('新手任务'),
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      )),

                                      // const SizedBox(width: 100),
                                      const Spacer(),
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            TIM_t('任务规则'),
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Color(0xFFFD8888),
                                            ),
                                          ),
                                          SizedBox(width: 4),
                                          Icon(
                                            Icons.chevron_right,
                                            color: Color(0xFFFD8888),
                                            size: 16,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  Positioned(
                                      left: 0,
                                      bottom: -8,
                                      child: Container(
                                        child: Image.asset(
                                            width: 42,
                                            height: 42,
                                            'assets/task_page/task_red.png'),
                                      )),
                                ],
                              )),
                        ],
                      ),
                      const SizedBox(height: 10),
                      // 任务一
                      _buildTaskItem(
                        taskNumber: TIM_t('任务一'),
                        taskDescription: TIM_t('注册后登录后领取'),
                        taskStatus: 1,
                      ),
                      const SizedBox(height: 16),
                      // 任务二
                      _buildTaskItem(
                        taskNumber: TIM_t('任务二'),
                        taskDescription: TIM_t('转发红包任务页面分享至Facebook和Instagram'),
                        taskStatus: 1,
                      ),
                      const SizedBox(height: 16),
                      // 任务三
                      _buildTaskItem(
                        taskNumber: TIM_t('任务三'),
                        taskDescription: TIM_t('邀请1名新用户注册'),
                        taskStatus: 2,
                      ),
                      const SizedBox(height: 16),

                      _buildTaskItem(
                        taskNumber: TIM_t('任务四'),
                        taskDescription: TIM_t('邀请3名好友组建群聊'),
                        taskStatus: 3,
                      ),
                      const SizedBox(height: 16),

                      _buildTaskItem(
                          taskNumber: TIM_t('任务五'),
                          taskDescription: TIM_t('充值 ₱10 并发红包到聊天群'),
                          taskStatus: 3,
                          isNextImg: false),

                      const SizedBox(height: 24),

                      // 底部奖励说明
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          gradient: const LinearGradient(
                            colors: [Color(0xFFFFDFD5), Color(0x00FDE8F2)],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                _buildShaderText(
                                    child: Text(
                                  TIM_t('完成任务'),
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                )),
                                const SizedBox(width: 8),
                                Expanded(
                                    child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      TIM_t('完成任务一至四') + ':',
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: Color(0xFF2D3748),
                                      ),
                                    ),
                                    Text(
                                      TIM_t('最高可得'),
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: Color(0xFF2D3748),
                                      ),
                                    ),
                                  ],
                                )),
                                Image.asset(
                                    'assets/task_page/task_next_right.png'),
                                Image.asset('assets/task_page/gold.png'),
                                Text(
                                  '₱20',
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Color(0xFFE53E3E),
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                _buildShaderText(
                                    child: Text(
                                  TIM_t('领取任务'),
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                )),
                                const SizedBox(width: 8),
                                Expanded(
                                    child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      TIM_t('完成任务五') + ':',
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: Color(0xFF2D3748),
                                      ),
                                    ),
                                    Text(
                                      TIM_t('最高可得'),
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: Color(0xFF2D3748),
                                      ),
                                    ),
                                  ],
                                )),
                                Image.asset(
                                    'assets/task_page/task_next_right.png'),
                                Image.asset('assets/task_page/gold.png'),
                                Text(
                                  '₱20',
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Color(0xFFE53E3E),
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                // Positioned(
                //   left: 0,
                //   right: 0,
                //   bottom: -20,
                //     child: Image.asset("assets/task_page/corner.png"))
              ],
            ),
            SizedBox(height: 8),
            Stack(
              clipBehavior: Clip.none,
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    image: const DecorationImage(
                      image: AssetImage('assets/task_page/task_card_bg.png'),
                      // Use AssetImage instead of Image.asset
                      fit: BoxFit.fill, // Optional, controls how the image fits
                    ),
                  ),
                  child: Column(
                    children: [
                      // 顶部标题栏
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                              padding: const EdgeInsets.symmetric(vertical: 6),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                gradient: const LinearGradient(
                                  colors: [
                                    Color(0xFFFFDFD5),
                                    Color(0x00FDE8F2)
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                              ),
                              child: Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  Row(
                                    children: [
                                      const SizedBox(width: 48),
                                      _buildShaderText(
                                          child: Text(
                                        TIM_t('我的邀请事业'),
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      )),

                                      // const SizedBox(width: 100),
                                      const Spacer(),
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            TIM_t('任务规则'),
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Color(0xFFFD8888),
                                            ),
                                          ),
                                          SizedBox(width: 4),
                                          Icon(
                                            Icons.chevron_right,
                                            color: Color(0xFFFD8888),
                                            size: 16,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  Positioned(
                                      left: 0,
                                      bottom: -8,
                                      child: Container(
                                        child: Image.asset(
                                            width: 42,
                                            height: 42,
                                            'assets/task_page/task_red.png'),
                                      )),
                                ],
                              )),
                          SizedBox(height: 8),

                          Container(
                            height: 38,
                            child: TextField(
                              textAlign: TextAlign.center, // 文本居中
                              decoration: InputDecoration(
                                fillColor: const Color(0xFFffdfd5),
                                // 设置背景颜色
                                filled: true,
                                // 启用背景颜色
                                hintText: TIM_t('输入好友验证码'),
                                // 提示文字
                                contentPadding: EdgeInsets.symmetric(
                                    vertical: 0, horizontal: 0),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  // 圆角
                                  borderSide: BorderSide.none, // 去除边框
                                ),
                                hintStyle:
                                    const TextStyle(color: Color(0xFFF69B7A)),
                                // 提示文字样式
                                suffixIcon: Padding(
                                  padding: EdgeInsets.only(right: 16),
                                  child: Image.asset(
                                    width: 16,
                                    height: 16,
                                    'assets/icon_edit.png',
                                  ),
                                ),
                                suffixIconConstraints: const BoxConstraints(
                                  minWidth: 16,
                                  minHeight: 16,
                                ),
                              ),
                            ),
                          ),

                          SizedBox(height: 8),
                          // 我的邀请码组件
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 9),
                            decoration: BoxDecoration(
                              // 渐变背景
                              gradient: const LinearGradient(
                                  colors: [
                                    Color(0xFFFDF2BC),
                                    Color(0xFFFFE171)
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: const Color(0xFFE8CC61), // 黄色边框
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                // 左侧文本部分
                                Expanded(
                                  child: Row(
                                    children: [
                                      Text(
                                        TIM_t('我的邀请码'),
                                        style: const TextStyle(
                                          fontSize: 12,
                                          color: Color(0xFF903D1F), // 棕色文字
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 8, vertical: 2),
                                        decoration: BoxDecoration(
                                          color: Colors.transparent,
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                        child: const Text(
                                          '[SANTOS-5XF9]',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Color(0xFF903D1F),
                                            // 棕色文字
                                            fontWeight: FontWeight.bold,
                                            letterSpacing: 0.5,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // 右侧图标部分
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    // 复制图标
                                    GestureDetector(
                                      onTap: () {
                                        // TODO: 实现复制功能
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          border: Border.all(
                                            color: const Color(0xFFE8CC61),
                                            // 黄色边框
                                            width: 1,
                                          ),
                                          gradient: const LinearGradient(
                                              colors: [
                                                Color(0xFFFDF2BC),
                                                Color(0xFFFFE171)
                                              ],
                                              begin: Alignment.topLeft,
                                              end: Alignment.bottomRight),
                                        ),
                                        child: const Icon(
                                          Icons.copy,
                                          size: 18,
                                          color: Color(0xFF903D1F),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    // 分享图标
                                    GestureDetector(
                                      onTap: () {
                                        // TODO: 实现分享功能
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          border: Border.all(
                                            color: const Color(0xFFE8CC61),
                                            // 黄色边框
                                            width: 1,
                                          ),
                                          gradient: const LinearGradient(
                                              colors: [
                                                Color(0xFFFDF2BC),
                                                Color(0xFFFFE171)
                                              ],
                                              begin: Alignment.topLeft,
                                              end: Alignment.bottomRight),
                                        ),
                                        child: const Icon(
                                          Icons.share,
                                          size: 18,
                                          color: Color(0xFF903D1F),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 20),
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.8),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Column(
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 19),
                                  decoration: BoxDecoration(
                                    color: Color(0xFFFFDFD5),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Container(
                                            width: 178,
                                            height: 26,
                                            decoration: BoxDecoration(
                                              image: const DecorationImage(
                                                image: AssetImage(
                                                    "assets/task_page/Invite_card.png"),
                                                fit: BoxFit.cover,
                                              ),
                                            ),
                                            child: Center(
                                              child: Text(
                                                TIM_t("邀请收益"),
                                                style: const TextStyle(
                                                  fontSize: 14,
                                                  color: Color(0xFFFFFFFF),
                                                  // 棕色文字
                                                  fontWeight: FontWeight.w400,
                                                ),
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                      SizedBox(height: 17),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Column(
                                            children: [
                                              Text(
                                                '50 ' + TIM_t("人"),
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: Color(0xFFE14D2A),
                                                  // 棕色文字
                                                  fontWeight: FontWeight.w400,
                                                ),
                                              ),
                                              SizedBox(height: 8),
                                              Text(
                                                TIM_t("已邀请"),
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: Color(0xFF903D1F),
                                                  // 棕色文字
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ],
                                          ),
                                          Container(
                                            height: 50,
                                            child: VerticalDivider(
                                              color: Color(0xFFF6CEC2),
                                              // 线条颜色
                                              thickness: 1,
                                              // 线条粗细
                                              width: 20,
                                              // 占的水平空间
                                              indent: 10,
                                              // 顶部缩进
                                              endIndent: 10, // 底部缩进
                                            ),
                                          ),
                                          Column(
                                            children: [
                                              Text(
                                                "₱10000",
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: Color(0xFFE14D2A),
                                                  // 棕色文字
                                                  fontWeight: FontWeight.w400,
                                                ),
                                              ),
                                              SizedBox(height: 8),
                                              Text(
                                                TIM_t("已获得奖励"),
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: Color(0xFF903D1F),
                                                  // 棕色文字
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ],
                                          ),
                                          Container(
                                            height: 50,
                                            child: VerticalDivider(
                                              color: Color(0xFFF6CEC2),
                                              // 线条颜色
                                              thickness: 1,
                                              // 线条粗细
                                              width: 20,
                                              // 占的水平空间
                                              indent: 10,
                                              // 顶部缩进
                                              endIndent: 10, // 底部缩进
                                            ),
                                          ),
                                          Column(
                                            children: [
                                              Text(
                                                "₱10000",
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: Color(0xFFE14D2A),
                                                  // 棕色文字
                                                  fontWeight: FontWeight.w400,
                                                ),
                                              ),
                                              SizedBox(height: 8),
                                              Text(
                                                TIM_t("待解锁奖励"),
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: Color(0xFF903D1F),
                                                  // 棕色文字
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                      Divider(
                                        color: Color(0xFFF6CEC2), // 线条颜色
                                        thickness: 1, // 线条粗细
                                        height: 12, // 占的垂直空间
                                      ),
                                      SizedBox(height: 12),
                                      DefaultTextStyle(
                                          style: const TextStyle(
                                            fontSize: 12,
                                            color: Color(0xFF903D1F),
                                            fontWeight: FontWeight.w500,
                                          ),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Expanded(
                                                child: Column(
                                                  children: [
                                                    RichText(
                                                        text: TextSpan(
                                                            style:
                                                                const TextStyle(
                                                              fontSize: 12,
                                                              color: Color(
                                                                  0xFF903D1F),
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                            ),
                                                            text: TIM_t_para(
                                                                    "再邀请{{option1}}人,即可领取",
                                                                    "再邀请3人,即可领取")(
                                                                option1: '3'),
                                                            children: [
                                                          TextSpan(
                                                              text: ' ₱1000 ' +
                                                                  TIM_t("奖励") +
                                                                  '!'),
                                                        ])),
                                                    const SizedBox(height: 10),
                                                    Row(
                                                      children: [
                                                        Expanded(
                                                          child: ClipRRect(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          8),
                                                              child:
                                                                  const LinearProgressIndicator(
                                                                value: 0.7,
                                                                minHeight: 4,
                                                                // 设置进度条高度
                                                                // 0~1，表示 60%
                                                                backgroundColor:
                                                                    Color(
                                                                        0xFFFFEFEF),
                                                                color: Color(
                                                                    0xFFE11600),
                                                              )),
                                                        ),
                                                        const SizedBox(
                                                            width: 4),
                                                        const Text("70%"),
                                                      ],
                                                    ),
                                                    const SizedBox(height: 12),
                                                  ],
                                                ),
                                              )
                                            ],
                                          )),
                                    ],
                                  ),
                                ),
                                SizedBox(height: 8),
                                Row(
                                  children: [
                                    // 左边渐变线
                                    Expanded(
                                      child: Container(
                                        height: 2,
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            colors: [
                                              Color(0x00E8CC61),
                                              Color(0xFFE8CC61)
                                            ], // 左边渐变色
                                          ),
                                        ),
                                        margin: EdgeInsets.only(
                                            right: 8), // 右侧留空给文字
                                      ),
                                    ),

                                    // 中间文字
                                    Text(
                                      TIM_t("已邀请好友"),
                                      style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        color: Color(0xFFDEB20C),
                                      ),
                                    ),

                                    // 右边渐变线
                                    Expanded(
                                      child: Container(
                                        height: 2,
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            colors: [
                                              Color(0xFFE8CC61),
                                              Color(0x00E8CC61),
                                            ], // 左边渐变色
                                          ),
                                        ),
                                        margin:
                                            EdgeInsets.only(left: 8), // 左侧留空给文字
                                      ),
                                    ),
                                  ],
                                ),
                                ConstrainedBox(
                                  constraints: BoxConstraints(
                                    maxHeight: 100, // 设置最大高度
                                  ),
                                  child: ListView.builder(
                                    padding: EdgeInsets.zero,
                                    shrinkWrap: true,
                                    // 确保 ListView 不会占用过多空间
                                    itemCount: invitedList.length,
                                    itemBuilder:
                                        (BuildContext context, int index) {
                                      final item = invitedList[index];
                                      return Container(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 19), // 设置左右内边距
                                        margin: EdgeInsets.symmetric(
                                            vertical: 5), // 设置上下间距（可选）
                                        child: Row(
                                          crossAxisAlignment: CrossAxisAlignment
                                              .center, // 垂直居中对齐
                                          children: [
                                            // 左侧的 CircleAvatar（头像）
                                            Container(
                                              width: 28, // 设置宽度
                                              height: 28, // 设置高度
                                              decoration: BoxDecoration(
                                                color: Color(0xFFD8D8D8),
                                                borderRadius: BorderRadius.circular(
                                                    10), // 设置矩形的圆角，如果不需要圆角可以去掉这一行
                                                // image: DecorationImage(
                                                //   image: NetworkImage('https://www.example.com/avatar.png'), // 设置头像图片
                                                //   fit: BoxFit.cover, // 图片填充方式
                                                // ),
                                              ),
                                            ),

                                            // 主要内容区域
                                            SizedBox(width: 10),
                                            // 控制头像和文本之间的间距
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment
                                                      .start, // 内容左对齐
                                              children: [
                                                Text(
                                                  item['phone']!,
                                                  style: TextStyle(
                                                    fontSize: 10,
                                                    fontWeight: FontWeight.w500,
                                                    color: Color(0xFF903D1F),
                                                  ),
                                                ),
                                                Text(
                                                  item['date']!,
                                                  style: TextStyle(
                                                    fontSize: 8,
                                                    fontWeight: FontWeight.w500,
                                                    color: Color(0xFF999999),
                                                  ),
                                                ),
                                              ],
                                            ),

                                            // 右侧的按钮区域
                                            Spacer(),
                                            // 填充剩余空间，使按钮右对齐
                                            Container(
                                              child: _buildTaskStatusBut(
                                                  status: item[
                                                      'status']!), // 自定义的按钮
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                    ],
                  ),
                ),
                // Positioned(
                //   left: 0,
                //   right: 0,
                //   bottom: -20,
                //     child: Image.asset("assets/task_page/corner.png"))
              ],
            ),
            const SizedBox(height: 10),
          ],
        )),
        Positioned(
          top: MediaQuery.of(context).padding.top, // 状态栏高度 + 10px
          left: 0,
          right: 0,
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Row(
                      children: [
                        IconButton(
                          color: Colors.white,
                          icon: const Icon(Icons.arrow_back_ios_new_outlined),
                          onPressed: () {
                            print("object");
                            Navigator.of(context).pop();
                          },
                        )
                      ],
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

// 辅助方法：构建任务项
}

class CheckInStatus {
  final String text; // 签到文本
  final Color textColor;
  final String icon; // 图标路径
  final dynamic bgTag; // 背景颜色，可能是单一颜色或颜色数组
  final Color border; // 边框颜色

  // 构造函数
  CheckInStatus({
    required this.textColor,
    required this.text,
    required this.icon,
    required this.bgTag,
    required this.border,
  });

  // 定义一个静态方法，根据状态返回相应的 CheckInStatus 对象
  static CheckInStatus fromStatus(int status) {
    // 状态数据
    final checkInStatus = {
      0: {
        "text": TIM_t("未签到"),
        "icon": "assets/task_page/no_checked_red.png",
        "bgTag": Colors.white,
        "border": Color(0xFFFFBBB3),
        "textColor": Color(0xFFE11600),
      },
      1: {
        "text": TIM_t("已签到"),
        "icon": "assets/task_page/checked_red.png",
        "bgTag": Color(0xFFE4E4E4),
        "border": Color(0xFFE4E4E4),
        "textColor": Color(0xFF666666),
      },
      2: {
        "text": TIM_t("签到"),
        "icon": "assets/task_page/no_checked_red.png",
        "bgTag": [Color(0xFFFFAA00), Color(0xFFDF1600)],
        "border": Colors.transparent,
        "textColor": Color(0xFFFFFFFFFF),
      },
    };

    // 确保状态存在

    final data = checkInStatus[status]!;
    return CheckInStatus(
      text: data['text'] as String,
      icon: data['icon'] as String,
      bgTag: data['bgTag'],
      textColor: data['textColor'] as Color,
      border: data['border'] as Color,
    );
  }
}

class TaskRedSign extends StatelessWidget {
  //0表示未签到,1已签到,2表示去签到(可签到),3表示究极大奖(一般是用于最后一个)
  final checkInStatusList = [1, 1, 2, 0, 0, 0, 0];

  final dayEnum = [
    TIM_t('第一天'),
    TIM_t('第二天'),
    TIM_t('第三天'),
    TIM_t('第四天'),
    TIM_t('第五天'),
    TIM_t('第六天'),
    TIM_t('第七天')
  ];

  int get totalSteps {
    return checkInStatusList.length;
  }

  int get currentStep {
    return checkInStatusList.lastIndexWhere((element) => element == 1) + 1;
  }

  double get progress {
    return currentStep / totalSteps + 0.06;
  }

  @override
  Widget build(BuildContext context) {
    return Column(children: [
      Container(
        child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: List.generate(7, (index) {
              final status = checkInStatusList[index];
              final item = CheckInStatus.fromStatus(status);
              return Stack(
                clipBehavior: Clip.none,
                children: [
                  Container(
                    height: 48,
                    child: Image.asset(
                        index < 6
                            ? item.icon
                            : 'assets/task_page/final_red.png',
                        width: 32,
                        height: 28),
                  ),
                  if (index == 6)
                    Positioned(
                      left: -20,
                      top: -5,
                      child: Container(
                        width: 60,
                        height: 18,
                        padding: EdgeInsets.only(bottom: 2),
                        decoration: BoxDecoration(
                            image: DecorationImage(
                          image:
                              AssetImage('assets/task_page/icon_message.png'),
                          fit: BoxFit.fill,
                        )),
                        child: Center(
                          child: Text(
                            TIM_t_para("最高可领{{option1}}", "最高可领₱100")(
                                option1: '₱100'),
                            style: TextStyle(
                              fontSize: 8,
                              color: Color(0xFFFFFFFF),
                            ),
                          ),
                        ),
                      ),
                    ),
                  Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        height: 16,
                        padding: const EdgeInsets.symmetric(horizontal: 1),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          gradient: item.bgTag is List<Color>
                              ? LinearGradient(
                                  colors: item.bgTag, // 如果是 List<Color>，使用渐变
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                )
                              : null,
                          border: Border.all(
                            color: item.border,
                            width: 1,
                          ),
                          color: item.bgTag is Color ? item.bgTag : null,
                          borderRadius: BorderRadius.circular(50),
                        ),
                        child: Text(
                          item.text,
                          style: TextStyle(
                            fontSize: 8,
                            color: item.textColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ))
                ],
              );
            }) as List<Widget>),
      ),
      SizedBox(
        height: 10,
      ),
      Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
              height: 3,
              child: LinearProgressIndicator(
                value: progress,
                minHeight: 3,
                // 设置进度条高度
                // 0~1，表示 60%
                backgroundColor: const Color(0xFFFFEFEF),
                color: const Color(0xFFE11600),
              )),
          Positioned(
              top: -3,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: List.generate(totalSteps, (index) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4.0),
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                            color: index < currentStep
                                ? Color(0xFFE11600)
                                : Color(0xFFFFFFFFF),
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: index < currentStep
                                  ? Color(0xFFE11600)
                                  : Color(0xFFFFBBB3), // 边框颜色
                              width: 1, // 边框宽度
                            )),
                      ),
                    );
                  }),
                ),
              ))
        ],
      ),
      SizedBox(
        height: 4,
      ),
      Container(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: List.generate(7, (index) {
            return Text(
              dayEnum[index],
              style: TextStyle(fontSize: 8, color: Color(0xFF666666)),
            );
          }),
        ),
      )
    ]);
  }
}

class TaskInfoButton extends StatelessWidget {
  final String amount;
  final String activatedCount;

  const TaskInfoButton({
    Key? key,
    required this.amount,
    required this.activatedCount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 30),
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/task_page/info_but.png'),
          fit: BoxFit.fill,
        ),
      ),
      child: Row(
        children: [
          // 已获得
          Row(
            children: [
              Text(
                TIM_t('已获得') + ' ',
                style: TextStyle(
                  color: Color(0xFF855D1D),
                  fontSize: 14,
                ),
              ),
              Text(
                '₱$amount',
                style: const TextStyle(
                  color: Color(0xffE14D2A),
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(width: 36),
          // 已激活
          Row(
            children: [
              Text(
                TIM_t('已激活') + ' ',
                style: TextStyle(
                  color: Color(0xFF855D1D),
                  fontSize: 14,
                ),
              ),
              Text(
                activatedCount,
                style: const TextStyle(
                  color: Color(0xffE14D2A),
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class AppLogo extends StatelessWidget {
  const AppLogo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double height = MediaQuery.of(context).size.height;
    final double width = MediaQuery.of(context).size.width;
    return Stack(
      children: [
        Container(
          child: Image.asset(
            'assets/task_page/task_page_bg.png',
            width: width,
            height: height,
            fit: BoxFit.cover,
          ),
        ),
      ],
    );
  }
}
